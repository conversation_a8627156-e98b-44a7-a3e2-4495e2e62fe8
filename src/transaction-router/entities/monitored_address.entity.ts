import { Index, Column, CreateDateColumn, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity('monitored_addresses')
export class MonitoredAddress {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  webhookId: string; // Which webhook endpoint cares about this address

  @Column()
  address: string;

  @Column()
  networkId: number;

  @Column({ default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @Index(['networkId', 'address'])
}